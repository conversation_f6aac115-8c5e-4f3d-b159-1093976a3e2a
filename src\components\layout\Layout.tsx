import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import MobileMenu from "../ui/MobileMenu";
import LoadingScreen from "../ui/LoadingScreen";
import EnhancedCursor from "../ui/EnhancedCursor";
import CookieConsentBanner from "../ui/CookieConsentBanner";
import CookieSettings from "../ui/CookieSettings";
import {
  initializeHashNavigation,
  navigateToSection,
} from "../../utils/navigation";

interface LayoutProps {
  children: React.ReactNode;
}

const LOGO_URL =
  "https://firebasestorage.googleapis.com/v0/b/gainback-9023c.appspot.com/o/test%2FCONALYS%20-%20White.png?alt=media&token=e341d001-3d37-4b75-b211-79cb8038b14b";

export default function Layout({ children }: LayoutProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState("home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);

      const sections = ["home", "features", "pricing", "contact"];
      const current = sections.find((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (current) setActiveSection(current);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
  }, [isMobileMenuOpen]);

  // Initialize hash navigation on component mount
  useEffect(() => {
    // Initialize immediately
    initializeHashNavigation();

    // Also handle when the document is fully loaded
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initializeHashNavigation);
    }

    // And handle when the window is fully loaded
    window.addEventListener("load", initializeHashNavigation);

    return () => {
      document.removeEventListener(
        "DOMContentLoaded",
        initializeHashNavigation
      );
      window.removeEventListener("load", initializeHashNavigation);
    };
  }, []);

  if (isLoading) {
    return <LoadingScreen logoUrl={LOGO_URL} />;
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-gray-950/80 backdrop-blur-lg shadow-lg shadow-gray-950/50"
            : "bg-transparent"
        }`}
      >
        <nav className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <motion.a
              href="/"
              className="block"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <img src={LOGO_URL} alt="CONALYS" className="h-12 w-auto" />
            </motion.a>

            <div className="hidden md:flex items-center space-x-1">
              {[
                { href: "#features", label: "Funktionen" },
                { href: "#pricing", label: "Preise" },
                { href: "#contact", label: "Kontakt" },
              ].map(({ href, label }) => (
                <motion.a
                  key={href}
                  href={href}
                  className={`relative px-4 py-2 text-sm font-medium tracking-tight transition-colors ${
                    activeSection === href.slice(1)
                      ? "text-white"
                      : "text-gray-400 hover:text-white"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {label}
                  {activeSection === href.slice(1) && (
                    <motion.div
                      layoutId="activeSection"
                      className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"
                      initial={false}
                    />
                  )}
                </motion.a>
              ))}

              <div className="w-px h-6 bg-gray-800 mx-4" />

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => navigateToSection("#pricing")}
                className="h-11 px-6 rounded-lg bg-white text-gray-900 font-medium hover:shadow-lg hover:shadow-white/10 transition-all duration-300"
              >
                Jetzt starten
              </motion.button>
            </div>

            <motion.button
              className="md:hidden relative z-50 w-10 h-10 flex items-center justify-center"
              whileTap={{ scale: 0.9 }}
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <div
                className={`w-6 h-0.5 bg-white relative transition-all duration-300 ${isMobileMenuOpen ? "rotate-45 translate-y-0.5" : ""} before:absolute before:w-6 before:h-0.5 before:bg-white before:transition-all before:duration-300 ${isMobileMenuOpen ? "before:-rotate-90 before:translate-y-0" : "before:-translate-y-2"} after:absolute after:w-6 after:h-0.5 after:bg-white after:transition-all after:duration-300 ${isMobileMenuOpen ? "after:opacity-0" : "after:translate-y-2"}`}
              />
            </motion.button>
          </div>
        </nav>
      </motion.header>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        logoUrl={LOGO_URL}
      />

      <main className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </main>

      <footer className="bg-gray-900 py-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              {/* <h3 className="text-xl font-bold mb-4">CONALYS</h3> */}
              <motion.a
                href="/"
                className="block"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <img src={LOGO_URL} alt="CONALYS" className="h-12 w-auto" />
              </motion.a>
              <p className="text-gray-400">
                Vertragsprüfungen. Einfach gemacht.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Links</h4>
              <ul className="space-y-2">
                <li>
                  <a href="/" className="text-gray-400 hover:text-white">
                    Home
                  </a>
                </li>
                <li>
                  <a
                    href="#features"
                    className="text-gray-400 hover:text-white"
                  >
                    Funktionen
                  </a>
                </li>
                <li>
                  <a href="#pricing" className="text-gray-400 hover:text-white">
                    Preise
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Rechtliches</h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="/imprint"
                    target="_blank"
                    className="text-gray-400 hover:text-white"
                  >
                    Impressum
                  </a>
                </li>
                <li>
                  <a
                    href="/privacy"
                    target="_blank"
                    className="text-gray-400 hover:text-white"
                  >
                    Datenschutzerklärung
                  </a>
                </li>
                <li>
                  <a
                    href="/terms"
                    target="_blank"
                    className="text-gray-400 hover:text-white"
                  >
                    Nutzungsbedingungen
                  </a>
                </li>
                {/* <li>
                  <a
                    href="/accessibility"
                    target="_blank"
                    className="text-gray-400 hover:text-white"
                  >
                    barrierefreiheitserklaerung
                  </a>
                </li>
                <li>
                  <a
                    href="/widerrufsbelehrung.pdf"
                    download="widerrufsbelehrung.pdf"
                    className="text-gray-400 hover:text-white"
                  >
                    widerrufsbelehrung
                  </a>
                </li> */}
                {/* <li>
                  <CookieSettings />
                </li> */}
                {/* <li>
                  <a href="/privacy" className="text-gray-400 hover:text-white">
                    Datenschutz
                  </a>
                </li>
                <li>
                  <a href="/terms" className="text-gray-400 hover:text-white">
                    Nutzungsbedingungen
                  </a>
                </li> */}
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Kontakt</h4>
              <ul className="space-y-2 text-gray-400">
                <li><EMAIL></li>
                <li>+49 160 954 70114</li>
                <li>CONALYS</li>
                <li>Dr. Bettina Oertel</li>
                <li>Berbisdorfer Str. 16</li>
                <li>D-09123 Chemnitz</li>
                <li>Germany</li>
              </ul>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-400">
            <p>
              &copy; {new Date().getFullYear()} Conalys - Dr. Bettina Oertel.
              Alle Rechte vorbehalten.
            </p>
          </div>
        </div>
      </footer>

      {/* Enhanced Cursor */}
      <EnhancedCursor />

      {/* Cookie Consent Banner */}
      {/* <CookieConsentBanner /> */}
    </div>
  );
}
