LICENSE TERMS AND TERMS OF USE
CONALYS
As of: July 13, 2025

www.conalys.de/licenseterms
This is a convenience translation for information purposes only and has no legal effect. Only the German version of these license terms and terms of use (available for download at www.conalys.de/lizenzbedingungen) is binding and decisive.

1.      Scope
    1.1 These license terms and terms of use ("Terms") govern the use of software ("Software"), including documentation and updates, provided to the customer ("you," "your," or "Customer”) by the Licensor as part of a subscription. Any conflicting or additional terms and conditions of the Customer shall not apply.
    Private individuals/consumers are not entitled to purchase the application (as defined below).
    "Use" means copying, downloading, installing, executing, accessing, displaying, using, or any other actions related to the Software.
    "Documentation" means all user manuals, handbooks, training materials, requirements, and other written or electronic materials provided by the Licensor for the Offer or arising from the use of the Offer.
    "Updates" means all upgrades, modified versions, updates, additions, and copies of the Software licensed to the Customer by the Licensor under these Terms and Conditions.
    Software, documentation, and Updates are collectively referred to as the "Application."
    1.2 The contract with the Licensor is concluded via the designated website of the Microsoft Office Store (also Microsoft AppSource) or Azure Marketplace (hereinafter collectively referred to as "Marketplace") at the prices, payment terms and rates, terms and services specified there. By subscribing to the Application via the Marketplace, you acknowledge that you have read and understood these Terms and that you agree to be bound by them. By accepting these terms, you enter into a binding and enforceable agreement with the Licensor for the use of the application in accordance with these terms (hereinafter referred to as the "Agreement"). If you do not agree to all of the terms, you may not download, install, or use the application.  
    1.3 Installation and configuration services are not covered by this Agreement.
    1.4 The Licensor reserves the exclusive right to determine, change, or update the configuration, functions, and features of the Application at its sole discretion. The Customer acknowledges and agrees that it is not entitled to demand, require, or rely on the availability of certain features, functions, or configurations of the Application unless expressly agreed in writing with the Licensor. This Agreement does not grant any implied rights to specific functions or future developments.
2.      License
    2.1 License Grant. Applications are licensed, not sold. Upon acceptance of an order and subject to the Customer's compliance with this Agreement, the Licensor grants the Customer a limited, non-exclusive license to use the ordered Application ("License"). The license is for the Customer's own use and business purposes only and is non-transferable and non-assignable (without the right to sublicense) unless expressly permitted in this Agreement or by applicable law.
    Applications may contain or be delivered with components that are subject to open source software licenses. The use of these components may be subject to additional terms and conditions, and the Customer agrees that all applicable licenses governing the use of the components are incorporated into this Agreement by reference.
    2.2 License Term. The license is granted on a subscription basis. The license expires at the end of the subscription period specified in the order, unless renewed. Licenses that are billed regularly based on usage will continue for as long as the Customer pays for the use of the Offerings.
    2.4 Hardware requirements. An Internet-enabled device is required to use the application. The Customer is solely responsible for ensuring that their hardware and environment are suitable for use with the application and are compatible with it.
    2.5 End Users. The Customer controls access to the Application by End Users and their use thereof and is responsible for any use of the Application that is not in accordance with this Agreement.
    2.6 Affiliated companies. The Customer may order applications for use by its affiliated companies (Section 15 of the German Stock Corporation Act (AktG), hereinafter referred to as "affiliated companies"). In this case, the licenses granted to the customer under this agreement shall also apply to these affiliated companies, but the Customer shall have the exclusive right to enforce this agreement against the licensor. The Customer remains responsible for all obligations under this Agreement and for compliance with this Agreement and all applicable orders by its affiliated companies.
    2.7 Reservation of rights. The Application is protected by German and international intellectual property laws and international treaties. The Licensor reserves all rights to the Application that are not expressly granted in this Agreement. All (property) rights, including all patents, trade secrets, copyrights, know-how, and other proprietary rights in the Application, including all parts of the Application contained in derivative works, are the exclusive property of the Licensor.
3.      Restrictions
    3.1 Unless expressly permitted in this Agreement, the documentation, or an order, the Customer is prohibited from (and is not authorized to):
    a) Copy, modify, reverse engineer, decompile, or disassemble the Applications or attempt to do so;
    b) Install or use third-party software or technologies in a manner that would subject the intellectual property or technology of the Licensor to other license terms;
    c) circumvent technical restrictions of an application or restrictions in the documentation;
    d) Separate parts of an application and/or run them on more than one device;
    e) Update or downgrade parts of an application at different times;
    f) use an application for illegal purposes;
    g) transfer parts of an application separately; or
    h) Distribute, sublicense, rent, lease, lend, use to provide hosting services for third parties, or otherwise make available to third parties, all or part of the applications.
    3.2 License transfers. The Customer may only transfer fully paid, perpetual licenses (1) to an affiliated company or (2) to a third party, solely in connection with the transfer of hardware to which the licenses were transferred or of employees to whom the licenses were assigned, as part of (A) a sale of all or part of an affiliated company or (B) a merger involving the Customer or an affiliated company of the Customer. After such transfer, the Customer must uninstall the licensed offering, cease using it, and render all copies unusable. The Customer must notify the Licensor of any license transfer and provide the transferee with a copy of this agreement and all other documents necessary to set forth the scope, purpose, and limitations of the transferred licenses. Attempted license transfers that do not comply with this section are invalid.
4.  Feedback; Use of Data
    4.1 Feedback. If the Customer decides to provide or make available to the Licensor suggestions, comments, ideas, improvements, or other feedback or materials related to the Software or otherwise (collectively, "Suggestions"), the Licensor shall be free to use, disclose, reproduce, modify, license, transfer, and otherwise use and distribute the Customer's Suggestions in any manner. The Customer shall have no claim to compensation or remuneration and shall not acquire any rights or shares in the application or other products of the Licensor that contain or are otherwise based on the Customer's suggestions, including, but not limited to, upgrades or modifications to the application.
    4.2 Use of Data. The Customer agrees that the Licensor may collect and use technical data and related information that is gathered periodically to facilitate the provision of software updates, product support, product development, and other services to the Customer and other customers in connection with the Application. The Licensor may collect, use, transfer, and disclose such information for any purpose, provided that it is in a form that does not contain confidential information or personal data. The Customer agrees to the use of anonymized log files for the purpose of further product development or the development of new products by the Licensor. The Licensor is also entitled to forward such files to its partners for the same purposes.
5.      Data protection
    5.1 EU Standard Contractual Clauses. To the extent applicable, the parties shall comply with the requirements of the data protection laws of the European Economic Area and Switzerland with regard to the collection, use, transfer, storage, and other processing of personal data from the European Economic Area and Switzerland. All transfers of Customer data from the European Union, the European Economic Area, and Switzerland are subject to the standard contractual clauses established by the European Commission, which are communicated to the Customer by the Licensor at the URL applicable to these Terms or by other means.
    5.2 Personal Data. The Customer consents to the processing of personal data by the Licensor and its affiliates and their respective agents and subcontractors in accordance with this Agreement. Prior to providing personal data to the Licensor, the Customer shall obtain all necessary consents from third parties (including the Customer's contacts, partners, distributors, administrators, and employees) in accordance with applicable data protection laws.
    5.3 Processing of personal data; GDPR. To the extent that the Licensor is a processor or subprocessor of personal data within the meaning of the GDPR, such processing shall be subject to the Standard Contractual Clauses, and the parties further agree to the following terms in this subsection ("Processing of personal data; GDPR"):
    a) Roles and responsibilities of the processor and the controller. The Customer and the Licensor agree that the Customer is the controller of personal data and the Licensor is the processor of such data, unless (a) the Customer acts as a processor of personal data, in which case the Licensor is a subprocessor, or (b) the Terms and Conditions state otherwise. The Licensor shall process personal data only on the documented instructions of the Customer. In all cases where the GDPR applies and the Customer is the data processor, the Customer warrants to the Licensor that the Customer's instructions, including the appointment of the data processor as data processor or sub-processor, have been approved by the relevant controller.
    b) Details of processing. The parties acknowledge and agree that:
    i. the subject matter of the processing is limited to personal data within the meaning of the GDPR;
    ii. the duration of the processing corresponds to the duration of the Customer 's right to use the application and ends with the deletion or return of all personal data in accordance with the Customer 's instructions or the provisions of this agreement;
    iii. the nature and purpose of the processing consist in the provision of the application in accordance with this Agreement;
    iv. the types of personal data processed by the offer include those expressly mentioned in Article 4 of the GDPR; and
    v. The categories of data subjects are representatives and end users of the Customer, such as employees, contractors, staff, and customers, as well as other data subjects whose personal data is contained in data provided to the licensor by the Customer.
    c) Rights of data subjects; assistance with requests. The Licensor shall provide the Customer with information in a manner consistent with the functionality of the Offer and the Licensor's role as a processor of personal data of data subjects, as well as with the ability to fulfill requests from data subjects to exercise their rights under the GDPR. The Licensor shall comply with reasonable requests from the Customer to assist it in responding to such requests from data subjects. If the Licensor receives a request from a data subject of the Customer who wishes to exercise one or more of their rights under the GDPR in connection with an Offer for which the Licensor acts as a data processor or sub-processor, the Licensor shall redirect the data subject to submit their request directly to the Customer. The Customer is responsible for responding to such requests, using the features of the offer where applicable. The licensor shall comply with reasonable requests from the Customer to assist in responding to such requests from data subjects.
    d) Use of Subprocessors. The Customer agrees that the Licensor may use the subprocessors listed at the Licensor's URL or otherwise communicated to the Customer. The Licensor remains responsible for compliance with the obligations contained herein by its Subprocessors. The Licensor may update its list of Subprocessors from time to time by notifying the Customer at least 14 days prior to granting access to personal data to a new Subprocessor. If the Customer does not agree to such changes, it may terminate any subscription to the affected offer without penalty by providing written notice of termination, stating the reasons for its disagreement, prior to the expiration of the notice period.
    e) Records of processing activities. The Licensor shall keep all records required under Article 30(2) of the GDPR and make them available to the Customer upon request, insofar as they are relevant to the processing of personal data on behalf of the Customer.
    5.4 Security. The Licensor shall take appropriate security measures as required by data protection laws and industry best practices in the field of data security.
    5.5 Support data. The Licensor may collect and use support data internally to provide technical support for the offering. The Licensor will not use support data for any other purpose unless the parties have agreed otherwise in writing.
6.      Review
    6.1 The Customer shall maintain records of its use or distribution of the Offerings by it and its affiliates. The Licensor may, at its own expense, verify the Customer's and its affiliates' compliance with this Agreement by engaging an independent auditor (subject to a confidentiality agreement) to conduct an audit or by requesting the Customer to conduct a self-audit. The Customer must immediately provide all information and documents reasonably requested by the Licensor or the auditor in connection with the review and access to the systems on which the offers are executed. If the review or self-audit reveals unlicensed use, the Customer must order sufficient licenses for the period of unlicensed use. Audits may be conducted more frequently if required by the auditors and/or regulatory authorities of the party, with respect to books and records related to this agreement. The costs of all such audits shall be borne by the party conducting the audit. All information and reports related to the audit process are confidential information and shall be used solely for the purpose of verifying compliance.
    6.2 Upon request, the Licensor shall provide the Customer with all information necessary to conduct an audit and verify compliance with the provisions of the GDPR for the processing of personal data. The Customer may request information via a security questionnaire or self-certification.
7.      Prices and payment
    7.1 All fees payable for the license rights granted under this Agreement are specified in the Licensor's respective offer. All prices are net prices plus the applicable statutory value-added tax. The Customer shall bear all taxes, duties, and costs incurred in connection with or arising from this Agreement (including all sales, use, source, consumption, or value-added taxes), except for taxes based on or calculated on the net profit of the Licensor, unless the Customer is exempt from paying such taxes and provides the Licensor with appropriate proof thereof.
    7.2 Microsoft will invoice the Customer in accordance with the Microsoft Commercial Marketplace Terms of Use and the applicable order.
8.  Support Services
9.  The Licensor is not obligated to provide technical support to the Customer unless this has been separately agreed in writing between the Customer and the Licensor.
10. The Licensor shall not be obliged to provide support services that are necessary due to: (i) the Customer's failure to use the Application in accordance with the documentation; (ii) information or materials provided by the Customer; (iii) a change to the Application or a change to its environment that has not been expressly agreed to in writing by the Licensor; (iv) the Customer's failure to use error correction measures, updates, or upgrades previously provided by the Licensor; (v) a malfunction of third-party hardware or software; or (vi) other causes beyond the reasonable control of the Licensor.
    8.3 The Licensor reserves the right to specify that only certain designated representatives of the Customer may contact the Licensor for support services.
    8.4 The Licensor may update the Application from time to time and make a new version available to the Customer. Previous versions of the Application will not be supported by the Licensor.
    8.5 The prices for the support service may be changed after prior notification to the Customer.
11. Warranty
    9.1 The Licensor warrants for the duration of the agreed contract term for the Application that it will be of the agreed quality and that this quality will be maintained, and that the Customer will be able to use the Application in accordance with the provisions of this Agreement without infringing the rights of third parties, provided that the Application is properly installed and used in accordance with the documentation.
    9.2 The Customer is obliged to notify the Licensor in writing of any defects in the application immediately after their discovery. In the case of material defects, this shall be done by describing the time of occurrence of the defects and the symptoms of the defects, and by submitting documentation illustrating the defects.
    9.3 If the Customer informs the Licensor in writing of a material defect in the application during the warranty period, the Licensor shall, at its own discretion and expense, either (a) remedy the defect, (b) deliver a replacement, or (c) refund the license fee paid for the defective part, subject to the Customer 's statutory rights. The Licensor is entitled to provide the warranty at the Customer's premises or remotely. The Licensor shall also fulfill its obligation to remedy defects by making updates available for download with an automatic installation routine and offering the Customer telephone support to resolve any installation problems that may arise.
    9.4 The warranty does not cover defects that are attributable to (a) unauthorized modifications, redesigns, or use of the application that is not in accordance with the documentation; (b) the use of the application in conjunction with hardware or software not supplied by the Licensor or to which the Licensor has not given its written consent; or (c) external influences beyond the reasonable sphere of influence, including force majeure. Furthermore, the warranty granted in this Agreement does not apply to versions of the application for which the Licensor has provided an update or a new version, but which have not been properly installed or implemented by the Customer, provided that the update or new version is necessary to remedy the defect or problem in question. If the Customer does not install or implement such an update within a reasonable period of time after notification, all warranty claims relating to defects that would have been remedied by the update are excluded.
    9.5 This warranty does not apply to open source components or third-party software provided with the application. Claims relating to such components can only be asserted against the third-party provider in accordance with its terms and conditions.
    9.6 The Licensor may refuse to provide warranty until the Customer has paid the agreed remuneration to the Licensor, less a portion corresponding to the economic significance of the defect. This does not apply to undisputed, legally established or final claims of the Customer.
    9.7 If the Customer asserts a defect against the Licensor and this defect cannot be identified or reproduced, or if the defect cannot be attributed to a warranty obligation of the Licensor after appropriate investigation (apparent defect) and the Customer could have recognized this, the Customer shall reimburse the Licensor for the costs and expenses incurred for verification and/or attempted rectification.
    9.8 The Licensor shall not be liable for defects that already exist at the time of conclusion of the contract, regardless of fault, in accordance with Section 536a (1) sentence 1 BGB.
    9.10 Insofar as the Licensor is obliged to provide a warranty, warranty claims shall become statute-barred within one (1) year of the start of the statutory limitation period, unless the Licensor is liable without limitation in accordance with the statutory provisions or the liability provisions set out in this agreement. This also applies to claims for compensation and the right of withdrawal in accordance with § 548 BGB.
    9.11 Insofar as the application depends on third-party software (e.g., operating system, browser) for its operation or use, it is only guaranteed that it is compatible with the Microsoft Office Word software specified in the documentation or, if not specified there, with the Microsoft Office Word software current at the time of conclusion of the contract. The Licensor does not guarantee that the software products will be compatible with later versions. The warranty also does not include the adaptation of the software to changed conditions of use and technical and functional developments such as changes in the IT environment, in particular changes to the hardware or operating system, adaptation to the functional scope of competing products, or the creation of compatibility with new data formats.
    9.12 All other express or implied warranties, including warranties of fitness for a particular purpose or merchantability, are excluded to the extent permitted by law. Information in the documentation, product descriptions, performance descriptions, or similar documents does not constitute a guarantee of specific characteristics of the application or its suitability for a particular purpose, unless this has been expressly agreed in writing between the parties.
12.     Liability
    10.1 The Licensor shall be liable without limitation for any legal grounds in the event of intent or gross negligence, in the event of intentional or negligent injury to life, limb or health, on the basis of a guarantee promise, unless otherwise stipulated in this regard, or on the basis of mandatory liability.
    10.2 If the platform operator negligently breaches an essential contractual obligation, liability shall be limited to the foreseeable damage typical for this type of contract, up to a maximum of EUR 50,000 per individual case, unless unlimited liability applies in accordance with the preceding paragraph. Essential contractual obligations are obligations that the contract imposes on the platform operator according to its content in order to achieve the purpose of the contract, the fulfillment of which is essential for the proper execution of the contract and on the observance of which the member may regularly rely. The Licensor shall only be liable for the loss or damage of data to the extent that such loss or damage could not have been prevented by appropriate data backup measures taken by the Customer. For applications provided free of charge and applications that the Customer may pass on to third parties without separate payment to the Licensor, the Licensor 's liability is limited to direct damage up to an amount of US$5,000.00.
    10.3 Any liability of the Licensor beyond the provisions of Sections 10.1 and 10.2 is excluded.
    10.4 The Licensor shall not be liable if the Customer has not exercised the care required for his professional activity.
    10.5 The above limitations and exclusions of liability shall also apply to the personal liability of the Licensor 's employees, representatives, and vicarious agents.
13. No further obligations
    This agreement does not establish any further obligations on the part of the Licensor that are not expressly set out in this agreement.
14.     Export control
    12.1 The parties acknowledge that the delivery, transfer, or use of the application and all related technical information or documentation ("deliverables") may be subject to the applicable export control and sanctions regulations of the European Union and its member states, the USA, and other relevant jurisdictions.
    12.2 The Customer undertakes not to export, re-export, transfer or make available the Application, either directly or indirectly, to any country, organization or person in violation of these export control laws and regulations, including, but not limited to Regulation (EU) 2021/821 (as amended) and any embargoes or restrictions imposed by the European Union, its member states, the USA or other competent authorities.
    12.3 The Customer further represents and warrants that it, its affiliates, and all end users (a) are not listed on any sanctions list of the EU, the US, or the United Nations, and (b) are not located in or acting on behalf of a country subject to sanctions or embargoes imposed by the EU, the US, or the United Nations.
    12.4 Prior to any export, re-export or transfer of the Application or any part thereof, the Customer shall obtain all licenses, permits or other approvals required under applicable law and shall provide the Licensor with proof of such approvals upon request.
    12.5 Any breach of this Article 12 by the Customer shall constitute a material breach of this Agreement and shall entitle the Licensor to terminate this Agreement with immediate effect and to seek all other remedies available under the Agreement and applicable law.
15. Indemnification
    The Customer shall indemnify and hold harmless the Licensor and its affiliates, and their respective agents, successors, and assigns, from and against any and all claims, liabilities, losses, damages, costs and expenses (including reasonable attorneys' fees and costs of legal action) arising out of or in connection with (i.) the breach of this Agreement and/or the violation of applicable laws and other governmental regulations, rules, norms, and standards by the Customer or its representatives; or (iii) the Licensor's use of the Customer Data in accordance with this Agreement.
16. Term and termination
    14.1 Term. The term of this Agreement shall be agreed upon at the time of execution and shall be disclosed to the Customer prior to execution. Subject to any agreements to the contrary , subscriptions may be terminated without notice at the end of the respective term.
    14.2 Termination. Termination shall be effected via the technical facilities provided for this purpose in the Customer area of the Microsoft Marketplace. The identity of the Customer can only be verified in this way.
    14.3 Renewal. If the contract is not terminated in due time, it shall be automatically extended for the originally booked term.
    14.4 The right to extraordinary termination without notice for good cause remains unaffected.
    14.5 Continuation. The provisions of this Agreement, including the applicable order, that are likely to require performance or apply to events that may occur after the termination or expiration of this Agreement or an order shall survive termination or expiration, including all indemnification obligations and procedures.
17. Free and open source software
    The Application may contain free and open source software components that are subject to the respective open source license agreements and may only be used on the basis of the applicable open source license agreements. These will be made available to the Customer upon request. The free and open source license agreements may contain provisions regarding the granting of rights of use as well as warranty and liability that differ from this agreement. In the event of any conflict between the provisions of this agreement and the free and open source license agreements, the free and open source license agreements shall prevail over the provisions of this agreement for the software components to which the free and open source license terms apply.
18. Confidentiality
    16.1 Confidentiality Agreement. The parties shall treat all confidential information exchanged between the parties in connection with this Agreement in accordance with the separate confidentiality agreement ("NDA") signed by the parties. If no separate NDA is in effect, the following provisions shall apply to the exchange of confidential information between the parties.
    16.2 Confidential Information. "Confidential Information" means non-public information that is marked "confidential" or that a reasonable person should understand to be confidential, including, but not limited to, Customer data, support data, the terms of this Agreement, and the Customer's login information. Confidential Information does not include information that: (1) becomes publicly available without breach of any confidentiality obligation; (2) is lawfully obtained by the receiving party from another source without any confidentiality obligation; (3) is independently developed; or (4) constitutes voluntary comments or suggestions about the other party's business, products, or services.
    16.3 Protection of confidential information. Each party shall take reasonable steps to protect the confidential information of the other party and shall use the confidential information of the other party solely for the purposes of the business relationship between the parties. Neither party shall disclose confidential information to any third party other than its representatives, and then only if necessary and subject to confidentiality obligations at least as strict as those set forth in this Agreement. Each party shall remain responsible for the use of confidential information by its representatives and shall notify the other party immediately if it becomes aware of any unauthorized use or disclosure.
    16.4 Disclosure required by law. A party may disclose the other party's confidential information if required to do so by law, but only after notifying the other party (if permitted by law) so that the other party may seek a protective order.
    16.5 Duration of Confidentiality Obligation. These obligations shall apply: (1) to Customer Data until its deletion by the Licensor; and (2) to all other Confidential Information for a period of five years after receipt of the Confidential Information by a party.
19. Miscellaneous
    17.1 Entire Agreement. This Agreement supersedes all prior and contemporaneous written or oral communications regarding the subject matter of this Agreement. In the event of any conflict between any part of this Agreement, the following shall prevail:
    a) Order confirmed by the Licensor;
    b) This Agreement;
    c) Documentation.
    17.2 Independent Contractors. The parties are independent contractors. The Customer and the Licensor may each independently develop products without using the other's confidential information.
    17.3 Non-exclusive agreement. The Customer is free to enter into agreements regarding the licensing, use, and advertising of the services of others.
    17.4 Amendments. The Licensor is entitled to amend these Terms and Conditions for objectively justified reasons (e.g., changes in case law, the legal situation, market conditions, or corporate strategy) and subject to reasonable notice. Existing customers will be notified of this at least two weeks before the change takes effect in the Marketplace. If the existing customer does not object within the period specified in the notification of change, their consent to the change shall be deemed to have been given. The notification of the intended change to these terms and conditions shall indicate the deadline and the consequences of objection or failure to object.
    17.5 Assignment. Either party may assign this Agreement to an affiliated company, but must notify the other party in writing of the assignment. The Customer agrees to assign all rights to which the Licensor is entitled under this agreement to an affiliated company or a third party without prior notice, including the right to payment and enforcement of the Customer's payment obligations, and all assignees may further assign these rights without further consent. In addition, either party may assign this Agreement without the consent of the other party in connection with a merger, reorganization, acquisition, or other transfer of all or substantially all of that party's assets. Any other proposed assignment of this Agreement must be approved in writing by the non-assigning party. The assignment shall not relieve the assigning party of its obligations under the assigned Agreement. Any attempted assignment without the required approval shall be ineffective.
    17.6 Severability. If any provision of this Agreement is found by a court of competent jurisdiction to be invalid, illegal, or unenforceable, that provision shall be enforced to the maximum extent permissible, and the remaining provisions shall remain in full force and effect. The parties shall negotiate in good faith to replace any invalid or unenforceable provision with a valid provision that most closely reflects the original intent of the parties.
    17.7 Waiver. Failure to enforce any provision of this Agreement shall not constitute a waiver. Any waiver must be in writing and signed by the waiving party.
    17.8 No Third Party Beneficiaries. This Agreement does not create any rights for third parties unless expressly provided in its terms.
    17.9 Notifications. Notifications must be made in writing and shall be deemed to have been delivered on the date of receipt at the address, on the date of the return receipt, on the date of email transmission, or on the date of delivery confirmation by the courier service or by fax. Notifications to the Licensor shall be sent to the address specified in the Order. Notifications to the Customer shall be sent to the person specified by the Customer in their account as the contact person for notifications. The Licensor may send notifications and other information to the Customer by email or in another electronic form.
    17.10 Applicable law. The law of the Federal Republic of Germany shall apply. The parties expressly agree that the United Nations Convention on Contracts for the International Sale of Goods (CISG) shall not apply to this agreement. The choice of law set forth herein excludes the application or effect of conflict of law rules or provisions of private international law that would permit or require the application of the law of another jurisdiction.
    17.11 Place of jurisdiction. If the Customer is a merchant or does not have a general place of jurisdiction in Germany, the parties agree that Munich shall be the place of jurisdiction for all disputes arising from the use of the platform. Exclusive places of jurisdiction remain unaffected.
    17.12 Order of precedence. The wording of this agreement shall take precedence over any conflicting provisions in other documents that form part of this agreement and have not been expressly repealed in these documents. The provisions of an amendment shall take precedence over the amended document and all previous amendments relating to the same subject matter.
    17.13 Public procurement regulations. By accepting this Agreement, the Customer represents and warrants that: (1) it has complied with and will comply with all applicable public procurement laws and regulations; (2) it is authorized to enter into this Agreement; and (3) this Agreement complies with all applicable procurement requirements.
    17.14 Compliance with Laws. The Licensor shall comply with all laws and regulations applicable to the provision of the Offerings. The Licensor shall obtain and maintain all permits, licenses, registrations, or approvals necessary for its performance and shall comply with all laws (including laws relating to export, corruption, money laundering, or any combination thereof). The Customer shall also comply with all laws applicable to its use of the Application.
    17.15 Interpretation. Neither party has entered into this Agreement in reliance on anything not contained or incorporated herein. This Agreement is written in German only. Any translation of this Agreement into another language is for informational purposes only and has no legal effect. If a court of competent jurisdiction declares any provision of this Agreement to be invalid, the Agreement shall be deemed amended to ensure its validity, and the remaining provisions of the Agreement shall remain in full force and effect to achieve the intent of the parties.

CONALYS, Dr. Bettina Oertel (Licensor)
Last update: 07 / 2025
