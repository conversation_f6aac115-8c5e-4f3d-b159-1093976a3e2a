import { motion } from "framer-motion";
import TermsContentGer from "../components/sections/TermsContentGer";
import TermsContentEng from "../components/sections/TermsContentEng";

export default function TermsConditions() {
  return (
    <section className="py-32 bg-gray-950 text-gray-200">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />

      {/* Animated Gradient Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 blur-3xl animate-drift" />
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-cyan-500/30 to-blue-500/30 blur-3xl animate-drift-slow" />
      </div>

      {/* <PERSON><PERSON> */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,black_40%,transparent_100%)]" />
      {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" /> */}
      <div className="container max-w-6xl mx-auto px-4">
        {/* German Terms Content */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors mb-16"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div className="relative z-10">
            <TermsContentGer />
          </div>
        </motion.div>

        {/* English Terms Content */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div className="relative z-10">
            <TermsContentEng />
          </div>
        </motion.div>
      </div>
    </section>
  );
}
